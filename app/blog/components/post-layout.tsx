import { MDXContent } from 'velite/react'
import { mdxComponents } from '@/components/mdx-components'
import type { CollectionEntry } from 'velite'

export interface PostLayoutProps {
  post: CollectionEntry<'Post'>
}

export function PostLayout({ post }: PostLayoutProps) {
  return (
    <article className="flex flex-col items-center max-w-[900px]">
      <div className="flex flex-col gap-2 items-center mb-8">
        <h1 className="text-2xl font-bold">{post.title}</h1>
        <p>{post.description}</p>
        <p>
          <b>{post.metadata.readingTime}</b> minute read
        </p>
      </div>

      <MDXContent code={post.body} components={mdxComponents} />
    </article>
  )
}
