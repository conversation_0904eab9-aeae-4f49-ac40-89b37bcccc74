import {SpeedInsights} from "@vercel/speed-insights/next"
import {Analytics} from "@vercel/analytics/next"
import type {<PERSON>ada<PERSON>} from "next";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono} from "next/font/google";
import "./globals.css";
import {ThemeProvider} from "@/components/theme-provider";
import {NavButton} from "@/components/nav-button";
import * as React from "react";
import {ModeToggle} from "@/components/mode-toggle";
import {CommandMenu} from "@/components/command-menu";
import {SocialButton} from "@/components/social-button";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IconBrandGithub} from "@tabler/icons-react";
import Link from "next/link";
import {FileJson, FileText, Rss} from "lucide-react";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Lu<PERSON>",
  description: "Personal website of <PERSON><PERSON>hiki<PERSON>",
  publisher: "Lukas <PERSON>hiki<PERSON>ki",
  creator: "Lukas Schikirianski",
  keywords: ["Lukas Schikirianski", "Lukas", "Schikirianski", "Blog", "Portfolio", "About", "Projects"],
  openGraph: {
    // TODO: Add opengraph image
    images: "/og-image.png"
  },
  metadataBase: new URL('https://schikirianskilukas.de'),
  other: {
    'application/rss+xml': '/posts/rss.xml',
    'application/atom+xml': '/posts/atom.xml',
    'application/json': '/posts/feed.json',
  },
};

export default function RootLayout({children}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <CommandMenu />
          <div className="font-sans flex flex-col min-h-screen px-8 pt-0 sm:px-10 gap-y-4">
            <header className="sticky top-0 flex items-center w-full pt-4 pb-4 bg-background">
              <div className="flex gap-2 sm:gap-4 relative sm:absolute sm:left-1/2 sm:transform sm:-translate-x-1/2">
                <NavButton href="/" title="Home" />
                <NavButton href="/blog" title="Blog" />
                <NavButton href="/projects" title="Projects" />
              </div>
              <div className="ml-auto">
                <ModeToggle />
              </div>
            </header>
            <main className="flex flex-col flex-1 items-center">
              <SpeedInsights />
              <Analytics />
              {children}
            </main>
            <footer className="flex mb-4 pt-2 gap-8 justify-between border-t-2 border-dashed border-primary bg-background">
              <div className="flex flex-col md:flex-row md:gap-x-4">
                <Link href="/posts/rss.xml" className="flex items-center gap-1 hover:underline">
                  <Rss className="w-4 h-4" /> RSS Feed
                </Link>
                <Link href="/posts/atom.xml" className="flex items-center gap-1 hover:underline">
                  <FileText className="w-4 h-4" /> Atom Feed
                </Link>
                <Link href="/posts/feed.json" className="flex items-center gap-1 hover:underline">
                  <FileJson className="w-4 h-4" /> JSON Feed
                </Link>
              </div>
              <div className="flex flex-col md:flex-row">
                <SocialButton
                  link="https://github.com/SleazeStiKs"
                  icon={<IconBrandGithub/>}
                />
                <SocialButton
                  link="https://bsky.app/profile/slzk.dev"
                  icon={<IconBrandBluesky/>}
                />
              </div>
            </footer>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
